//  H5WebViewController.m
//  Nano_Loan

#import "H5WebViewController.h"
#import <MBProgressHUD/MBProgressHUD.h>
#import "RiskEventManager.h"          // 埋点上报
#import <StoreKit/StoreKit.h>          // App Store 评分
#import "CustomTabBarController.h"    // 首页 Tab 切换
#import "CustomTabBar.h"              // 操作自定义 TabBar
#import "NetworkManager.h"

@interface H5WebViewController ()
{
    WKWebView *_webView;
}
@property (nonatomic, copy) NSString *urlString;
@property (nonatomic, strong) UIProgressView *progressView;
/// 绑卡开始时间（秒级时间戳）
@property (nonatomic, assign) NSTimeInterval bindCardStartTime;
/// 每秒更新标题的定时器
@property (nonatomic, strong, nullable) NSTimer *titleTimer;
/// 缓存上一次设置到导航栏的标题，避免重复赋值
@property (nonatomic, copy, nullable) NSString *lastTitle;
@end

@implementation H5WebViewController

#pragma mark - Helper
- (void)switchToTabIndex:(NSInteger)index {
    UITabBarController *tab = self.tabBarController;
    if ([tab isKindOfClass:[CustomTabBarController class]]) {
        CustomTabBarController *custom = (CustomTabBarController *)tab;
        custom.selectedIndex = index;
        if ([custom.customTabBar respondsToSelector:@selector(setSelectedIndex:)]) {
            [custom.customTabBar setSelectedIndex:index];
        }
    } else {
        tab.selectedIndex = index;
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self startTitleTimer];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self stopTitleTimer];
}

- (instancetype)initWithURLString:(NSString *)urlString {
    self = [super init];
    if (self) {
        _urlString = [urlString copy];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // 初始不设置标题，等待网页加载完成后再设置
    self.navigationItem.title = nil;

    [self setupWebView];
    [self loadRequest];
    
    // 使用本地返回图标，使用模板模式以便应用 tintColor
    UIImage *backImage = [UIImage imageNamed:@"nav_back"];
    UIBarButtonItem *backItem = [[UIBarButtonItem alloc] initWithImage:backImage style:UIBarButtonItemStylePlain target:self action:@selector(backAction)];
    self.navigationItem.leftBarButtonItem = backItem;
    
    // 显示导航栏
    [self.navigationController setNavigationBarHidden:NO animated:YES];

    // 设置导航栏背景颜色为 #6A81FE
    UIColor *navBarColor = [UIColor colorWithRed:0.416f green:0.506f blue:0.996f alpha:1.0f]; // #6A81FE

    if (@available(iOS 13.0, *)) {
        // iOS 13+ 使用新的外观API
        UINavigationBarAppearance *appearance = [[UINavigationBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = navBarColor;

        // 设置标题颜色为白色
        appearance.titleTextAttributes = @{NSForegroundColorAttributeName: [UIColor whiteColor]};
        appearance.largeTitleTextAttributes = @{NSForegroundColorAttributeName: [UIColor whiteColor]};

        self.navigationController.navigationBar.standardAppearance = appearance;
        self.navigationController.navigationBar.scrollEdgeAppearance = appearance;
        self.navigationController.navigationBar.compactAppearance = appearance;

        // 设置导航栏按钮颜色为白色
        self.navigationController.navigationBar.tintColor = [UIColor whiteColor];
    } else {
        // iOS 13 以下使用传统方式
        self.navigationController.navigationBar.barTintColor = navBarColor;
        self.navigationController.navigationBar.translucent = NO;

        // 设置标题颜色为白色
        self.navigationController.navigationBar.titleTextAttributes = @{NSForegroundColorAttributeName: [UIColor whiteColor]};

        // 设置导航栏按钮颜色为白色
        self.navigationController.navigationBar.tintColor = [UIColor whiteColor];
    }
}

- (void)setupWebView {
    WKWebViewConfiguration *config = [[WKWebViewConfiguration alloc] init]; // 清空 JS 注入与 messageHandler

    // 重新创建 UserContentController，并直接注册前端约定的 message handler 名称
    WKUserContentController *userContentController = [[WKUserContentController alloc] init];

    NSArray<NSString *> *handlerNames = @[@"extremely", @"caught", @"rightagain", @"speaking", @"Shecould", @"happened", @"pretended"];
    for (NSString *name in handlerNames) {
        [userContentController addScriptMessageHandler:self name:name];
    }

    config.userContentController = userContentController;

    _webView = [[WKWebView alloc] initWithFrame:self.view.bounds configuration:config];
    _webView.navigationDelegate = self;
    _webView.UIDelegate = self;
    _webView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.view addSubview:_webView];

    self.progressView = [[UIProgressView alloc] initWithProgressViewStyle:UIProgressViewStyleDefault];
    self.progressView.frame = CGRectMake(0, self.navigationController.navigationBar.bounds.size.height - 2, self.view.bounds.size.width, 2);

    // 设置进度条颜色为 #E3FAFF，区分下方白色网页
    UIColor *progressColor = [UIColor colorWithRed:0.890f green:0.980f blue:1.0f alpha:1.0f]; // #E3FAFF
    self.progressView.progressTintColor = progressColor;

    [self.navigationController.navigationBar addSubview:self.progressView];

    [_webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:nil];
}

- (void)dealloc {
    // 移除 observer & messageHandler
    [_webView removeObserver:self forKeyPath:@"estimatedProgress"];
    NSArray<NSString *> *handlerNames = @[@"extremely", @"caught", @"rightagain", @"speaking", @"Shecould", @"happened", @"pretended"];
    for (NSString *name in handlerNames) {
        [_webView.configuration.userContentController removeScriptMessageHandlerForName:name];
    }
    [self stopTitleTimer];
}

- (void)loadRequest {
    NSURLComponents *components = [NSURLComponents componentsWithString:self.urlString];
    if (!components) return;

    // 为 H5 URL 拼接固定公共参数，保持与接口一致
    [NetworkManager appendCommonParamsToComponents:components];

    NSURL *url = components.URL;
    if (!url) return;
    [_webView loadRequest:[NSURLRequest requestWithURL:url]];
}

#pragma mark - KVO
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    if ([keyPath isEqualToString:@"estimatedProgress"]) {
        self.progressView.progress = _webView.estimatedProgress;
        self.progressView.hidden = (_webView.estimatedProgress >= 1.0);
    }
}

#pragma mark - WKNavigationDelegate
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    self.progressView.hidden = NO;
    self.progressView.progress = 0;
}
- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    self.progressView.hidden = YES;
}
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    self.progressView.hidden = YES;
    // 从网页 title 设置导航标题，如果网页没有标题则不显示标题
    NSString *webTitle = webView.title;
    NSLog(@"[H5WebViewController] Web title: '%@'", webTitle);

    if (webTitle && webTitle.length > 0) {
        self.navigationItem.title = webTitle;
        self.lastTitle = webTitle;
        NSLog(@"[H5WebViewController] Set navigation title to: '%@'", webTitle);
    } else {
        // 如果网页没有标题，则不显示标题
        self.navigationItem.title = nil;
        self.lastTitle = nil;
        NSLog(@"[H5WebViewController] No web title, clearing navigation title");
    }
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURL *url = navigationAction.request.URL;
    NSLog(@"[WKNav] actionType=%ld URL=%@", (long)navigationAction.navigationType, url.absoluteString);
    decisionHandler(WKNavigationActionPolicyAllow);
}

#pragma mark - WKUIDelegate
// 处理 JS alert
- (void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Alert" message:message preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        completionHandler();
    }]];
    [self presentViewController:alert animated:YES completion:nil];
}

// 自定义返回动作
- (void)backAction {
    if (self.navigationController) {
        if (self.navigationController.viewControllers.firstObject == self) {
            // 作为根控制器被 modal Present
            [self.navigationController dismissViewControllerAnimated:YES completion:nil];
        } else {
            // 普通 push
            [self.navigationController popViewControllerAnimated:YES];
        }
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    NSString *name = message.name;
    id body = message.body;

    // 公共解析
    NSString *urlString = nil;
    NSString *params = nil;
    NSString *orderId = nil;
    if ([body isKindOfClass:[NSDictionary class]]) {
        NSDictionary *dict = (NSDictionary *)body;
        urlString = dict[@"url"];
        params = dict[@"params"];
        orderId = dict[@"orderId"];
    } else if ([body isKindOfClass:[NSString class]]) {
        // 如果直接传字符串，当作 url 或 orderId
        urlString = (NSString *)body;
        orderId = (NSString *)body;
    }

    if ([name isEqualToString:@"extremely"]) {
        // 关闭当前 webview
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }

    if ([name isEqualToString:@"caught"]) {
        if (urlString.length == 0) { return; }
        NSString *fullURL = urlString;
        if (params.length > 0) {
            fullURL = [urlString containsString:@"?"] ? [urlString stringByAppendingFormat:@"&%@", params] : [urlString stringByAppendingFormat:@"?%@", params];
        }
        H5WebViewController *vc = [[H5WebViewController alloc] initWithURLString:fullURL];
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }

    if ([name isEqualToString:@"rightagain"]) {
        [self switchToTabIndex:0];
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }

    if ([name isEqualToString:@"speaking"]) {
        if (@available(iOS 14.0, *)) {
            UIWindowScene *windowScene = [[[UIApplication sharedApplication] connectedScenes] anyObject];
            if (windowScene) {
                [SKStoreReviewController requestReviewInScene:windowScene];
            } else {
                [SKStoreReviewController requestReview];
            }
        } else {
            [SKStoreReviewController requestReview];
        }
        return;
    }

    if ([name isEqualToString:@"Shecould"]) {
        NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
        [RiskEventManager reportEventType:RiskEventTypeEndLoan startTime:now endTime:now orderId:orderId];
        return;
    }

    if ([name isEqualToString:@"happened"]) {
        self.bindCardStartTime = [[NSDate date] timeIntervalSince1970];
        return;
    }

    if ([name isEqualToString:@"pretended"]) {
        NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
        if (self.bindCardStartTime == 0) {
            self.bindCardStartTime = endTime;
        }
        [RiskEventManager reportEventType:RiskEventTypeBindCard startTime:self.bindCardStartTime endTime:endTime orderId:nil];
        return;
    }
}

- (NSString *)extractTitleFromURL:(NSString *)urlString {
    // 这里可以根据实际 URL 结构提取标题，以下是一个简单实现
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) return nil;
    
    NSString *host = url.host;
    NSString *path = url.path;
    
    // 去除 www 和顶级域名
    host = [host stringByReplacingOccurrencesOfString:@"www." withString:@""];
    NSRange tldRange = [host rangeOfString:@"." options:NSBackwardsSearch];
    if (tldRange.location != NSNotFound) {
        host = [host substringToIndex:tldRange.location];
    }
    
    // 如果有路径，取路径的最后一部分
    if (path.length > 1) {
        NSString *lastPathComponent = [path lastPathComponent];
        if (lastPathComponent.length > 0) {
            return lastPathComponent;
        }
    }
    
    return host;
}

- (void)startTitleTimer {
    if (self.titleTimer) { return; }
    // 使用 CommonModes，保证滚动时也能更新
    NSTimer *timer = [NSTimer timerWithTimeInterval:1.0 target:self selector:@selector(updateTitleTick) userInfo:nil repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
    self.titleTimer = timer;
}

- (void)stopTitleTimer {
    if (self.titleTimer) {
        [self.titleTimer invalidate];
        self.titleTimer = nil;
    }
}

- (void)updateTitleTick {
    NSString *webTitle = _webView.title;
    if (webTitle.length == 0) {
        webTitle = nil;
    }
    BOOL changed = NO;
    if (self.lastTitle == nil && webTitle != nil) {
        changed = YES;
    } else if (self.lastTitle != nil && webTitle == nil) {
        changed = YES;
    } else if (self.lastTitle && webTitle && ![self.lastTitle isEqualToString:webTitle]) {
        changed = YES;
    }
    if (changed) {
        self.navigationItem.title = webTitle;
        self.lastTitle = [webTitle copy];
        NSLog(@"[H5WebViewController] (timer) Update title: %@", webTitle);
    }
}

@end 


/*
 
 ### 原生 h5 交互函数
```
extremely()关闭当前webview
caught(String url, String params)带参数页面跳转
rightagain()回到首页，并关闭当前页
speaking() app store评分功能
Shecould() 确认申请埋点调用方法
happened() 开始绑卡
pretended() 结束绑卡
```
 */
