# 动态域名切换功能测试指南

## 功能概述

本功能实现了每次启动都下载动态域名配置，并在默认接口不可用时自动切换到备用域名的机制：

1. **每次启动流程**：
   - 下载并缓存动态域名JSON到本地
   - 先用默认域名测试初始化接口
   - 如果默认域名失败，从缓存的JSON中获取备用域名
   - 找到可用域名后，替换全局域名

2. **域名配置**：
   - **默认域名**: `https://nano.paperplane-lending.com/youtoday`
   - **动态域名配置**: `https://ph4-dc.oss-ap-southeast-1.aliyuncs.com/nano-loan/nano.json`

3. **超时设置**：
   - 动态域名相关接口：10秒超时
   - 其他业务接口：30秒超时

4. **Loading显示**: 所有动态域名相关请求都显示loading

## 实现的类

### 1. DynamicDomainManager
- 负责管理动态域名切换逻辑
- 获取动态域名列表
- 测试域名可用性
- 缓存可用域名（24小时有效期）

### 2. NetworkManager (修改)
- 添加了支持自定义超时时间的方法
- 添加了loading显示/隐藏功能
- 默认超时时间改为30秒

### 3. LaunchViewController (修改)
- 集成动态域名切换逻辑
- 在默认域名失败后自动尝试动态域名
- 显示loading和错误提示

## 测试步骤

### 1. 正常流程测试
1. 启动应用
2. 观察控制台日志，应该看到：
   ```
   [Launch] 启动动态域名处理流程
   [DynamicDomain] 开始下载动态域名JSON
   [Launch] 动态域名JSON下载成功，开始测试默认接口
   [Launch] 开始测试默认域名
   [Launch] 使用默认域名请求初始化接口
   [NetworkManager] 表单请求: Alicia/bigger (超时: 10.0s)
   [Launch] 默认域名初始化请求成功
   ```
3. 应用正常进入主页面

### 2. 动态域名切换测试
为了测试动态域名切换，可以：

#### 方法1: 修改默认域名（模拟网络问题）
1. 在LaunchViewController.m的viewDidAppear方法中，临时修改默认域名：
   ```objc
   [NetworkManager setBaseURL:@"https://invalid-domain.com"];
   ```
2. 启动应用
3. 观察控制台日志，应该看到动态域名切换流程

#### 方法2: 使用测试方法
1. 在LaunchViewController的viewDidAppear方法中添加：
   ```objc
   [self testDynamicDomainSwitch];
   ```
2. 启动应用
3. 观察控制台日志

#### 方法3: 单独测试JSON获取
1. 在LaunchViewController的viewDidAppear方法中添加：
   ```objc
   [[DynamicDomainManager sharedManager] testDynamicDomainFunction];
   ```
2. 这会先单独测试JSON获取，再测试完整流程

### 3. 预期的日志输出

#### 成功的动态域名切换流程：
```
[Launch] 启动动态域名处理流程
[DynamicDomain] 开始下载动态域名JSON
[DynamicDomain] 成功获取动态域名配置: [{"nano":"http://47.84.52.237/youtoday/"}]
[DynamicDomain] 已缓存动态域名JSON
[Launch] 动态域名JSON下载成功，开始测试默认接口
[Launch] 默认域名两次请求都失败，开始动态域名切换
[DynamicDomain] 从缓存中切换到可用域名
[DynamicDomain] 从缓存JSON中找到 1 个域名
[DynamicDomain] 测试域名 1/1: http://47.84.52.237/youtoday
[DynamicDomain] 域名连通性测试通过，进行API测试
[DynamicDomain] 找到可用域名: http://47.84.52.237/youtoday
[Launch] 动态域名切换成功，新域名: http://47.84.52.237/youtoday
[Launch] 使用新域名请求初始化接口
[Launch] 动态域名初始化请求成功
```

## 功能特性

### 1. JSON缓存机制
- 每次启动都下载最新的动态域名JSON
- JSON缓存到本地UserDefaults
- 从缓存的JSON中解析域名列表进行测试

### 2. 超时控制
- JSON获取：15秒超时
- 域名连通性测试：8秒超时
- 域名API测试：12秒超时
- 其他业务接口：30秒超时

### 3. Loading显示
- 在动态域名切换过程中显示全屏loading
- 自动在请求完成后隐藏

### 4. 错误处理
- 如果JSON获取失败，使用备用域名列表
- 如果所有域名都不可用，显示网络错误提示
- 支持重试机制

### 5. 多层测试机制
- 先进行简单的连通性测试
- 连通性通过后再进行完整的API测试
- 提高测试效率和成功率

### 6. 备用域名机制
- 当缓存的JSON无可用域名时，使用硬编码的备用域名列表
- 包含已知可用的域名：`http://47.84.52.237/youtoday`

### 7. 全局域名替换
- 找到可用域名后，通过NetworkManager.setBaseURL()替换全局域名
- 后续所有网络请求都使用新域名

## 注意事项

1. 动态域名JSON格式支持两种：
   - 对象数组：`[{"nano": "domain1"}, {"nano": "domain2"}]`
   - 字符串数组：`["domain1", "domain2"]`

2. 域名会自动处理尾部斜杠

3. 测试时建议使用真实设备或模拟器的网络环境

4. 生产环境中应移除测试方法调用

## 故障排除

### 1. JSON获取超时问题
如果遇到JSON获取超时，可以检查：
- 网络连接是否正常
- 是否有防火墙或代理阻止请求
- OSS服务是否正常

解决方案：
- 增加了备用域名机制，即使JSON获取失败也能正常工作
- 可以在备用域名列表中添加更多已知可用的域名

### 2. 域名测试失败
如果域名连通性测试失败：
- 检查域名是否可以在浏览器中访问
- 确认域名支持HTTP/HTTPS协议
- 检查是否有CORS或其他限制

### 3. 调试日志
关键日志标识：
- `[DynamicDomain]` - 动态域名管理器日志
- `[Launch]` - 启动页日志
- `[NetworkManager]` - 网络请求日志

### 4. 常见错误码
- `-1` - 域名配置格式错误
- `-2` - 未找到可用域名
- 网络错误 - 通常是超时或连接失败

### 5. 手动测试步骤
1. 在浏览器中访问：`https://ph4-dc.oss-ap-southeast-1.aliyuncs.com/nano-loan/nano.json`
2. 确认返回的JSON格式正确
3. 手动测试备用域名：`http://47.84.52.237/youtoday/Alicia/bigger`
