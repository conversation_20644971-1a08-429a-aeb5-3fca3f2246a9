#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 动态域名切换完成回调
/// @param success 是否成功找到可用域名
/// @param availableDomain 可用的域名，如果成功的话
/// @param error 错误信息，如果失败的话
typedef void(^DynamicDomainCompletion)(BOOL success, NSString * _Nullable availableDomain, NSError * _Nullable error);

/// 动态域名管理器
/// 负责管理动态域名切换逻辑，包括获取动态域名列表、测试域名可用性、缓存可用域名等功能
@interface DynamicDomainManager : NSObject

/// 单例实例
+ (instancetype)sharedManager;

/// 默认域名
@property (nonatomic, readonly) NSString *defaultDomain;

/// 动态域名配置文件URL
@property (nonatomic, readonly) NSString *dynamicDomainConfigURL;

/// 当前使用的域名
@property (nonatomic, readonly, nullable) NSString *currentDomain;

/// 下载并缓存动态域名JSON到本地
/// @param completion 完成回调
- (void)downloadAndCacheDynamicDomainJSON:(void(^)(BOOL success, NSError * _Nullable error))completion;

/// 从缓存的JSON中切换到可用域名
/// @param completion 完成回调
- (void)switchToAvailableDomainFromCache:(DynamicDomainCompletion)completion;

/// 测试指定域名是否可用
/// @param domain 要测试的域名
/// @param completion 测试完成回调
- (void)testDomainAvailability:(NSString *)domain completion:(void(^)(BOOL available))completion;

/// 简单的网络连通性测试
/// @param domain 要测试的域名
/// @param completion 测试完成回调
- (void)testDomainConnectivity:(NSString *)domain completion:(void(^)(BOOL available))completion;

/// 重置到默认域名
- (void)resetToDefaultDomain;

/// 获取缓存的可用域名
- (nullable NSString *)getCachedAvailableDomain;

/// 清除域名缓存
- (void)clearDomainCache;

/// 测试动态域名功能（仅用于调试）
- (void)testDynamicDomainFunction;

@end

NS_ASSUME_NONNULL_END
