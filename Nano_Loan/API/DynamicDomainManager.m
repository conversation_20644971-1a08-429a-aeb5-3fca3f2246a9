#import "DynamicDomainManager.h"
#import <AFNetworking/AFNetworking.h>

// 缓存键
static NSString * const kCachedAvailableDomainKey = @"CachedAvailableDomain";
static NSString * const kDomainCacheTimestampKey = @"DomainCacheTimestamp";
static NSString * const kDynamicDomainJSONCacheKey = @"DynamicDomainJSONCache";

// 缓存有效期（24小时）
static const NSTimeInterval kDomainCacheValidDuration = 24 * 60 * 60;

@interface DynamicDomainManager ()
@property (nonatomic, strong) NSString *defaultDomain;
@property (nonatomic, strong) NSString *dynamicDomainConfigURL;
@property (nonatomic, strong, nullable) NSString *currentDomain;
@end

@implementation DynamicDomainManager

+ (instancetype)sharedManager {
    static DynamicDomainManager *_sharedManager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedManager = [[self alloc] init];
    });
    return _sharedManager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _defaultDomain = @"https://nano.paperplane-lending.com/youtoday";
        _dynamicDomainConfigURL = @"https://ph4-dc.oss-ap-southeast-1.aliyuncs.com/nano-loan/nano.json";
        _currentDomain = _defaultDomain;
    }
    return self;
}

#pragma mark - Public Methods

- (void)downloadAndCacheDynamicDomainJSON:(void(^)(BOOL success, NSError * _Nullable error))completion {
    NSLog(@"[DynamicDomain] 开始下载动态域名JSON");

    // 创建请求管理器，设置10秒超时
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];

    // 设置响应序列化器，支持更多内容类型
    AFJSONResponseSerializer *responseSerializer = [AFJSONResponseSerializer serializer];
    responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/json", @"text/javascript", @"text/plain", nil];
    manager.responseSerializer = responseSerializer;

    // 设置请求序列化器
    AFHTTPRequestSerializer *requestSerializer = [AFHTTPRequestSerializer serializer];
    requestSerializer.timeoutInterval = 10.0; // 10秒超时
    [requestSerializer setValue:@"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15" forHTTPHeaderField:@"User-Agent"];
    manager.requestSerializer = requestSerializer;

    // 设置安全策略，允许HTTP请求
    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy defaultPolicy];
    securityPolicy.allowInvalidCertificates = YES;
    securityPolicy.validatesDomainName = NO;
    manager.securityPolicy = securityPolicy;

    NSLog(@"[DynamicDomain] 请求URL: %@", self.dynamicDomainConfigURL);

    [manager GET:self.dynamicDomainConfigURL parameters:nil headers:nil progress:nil
         success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSLog(@"[DynamicDomain] 成功获取动态域名配置: %@", responseObject);

        // 缓存JSON到本地
        [self cacheDynamicDomainJSON:responseObject];

        if (completion) completion(YES, nil);
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"[DynamicDomain] 获取动态域名配置失败: %@", error.localizedDescription);
        if (completion) completion(NO, error);
    }];
}

- (void)switchToAvailableDomainFromCache:(DynamicDomainCompletion)completion {
    NSLog(@"[DynamicDomain] 从缓存中切换到可用域名");

    // 先尝试从缓存的JSON中获取域名列表
    NSArray *domainList = [self getDomainListFromCache];

    if (domainList.count > 0) {
        NSLog(@"[DynamicDomain] 从缓存JSON中找到 %lu 个域名", (unsigned long)domainList.count);
        [self testDomainsSequentially:domainList currentIndex:0 completion:completion];
    } else {
        NSLog(@"[DynamicDomain] 缓存中无可用域名，使用备用域名");
        [self tryFallbackDomains:completion];
    }
}

- (void)testDomainAvailability:(NSString *)domain completion:(void(^)(BOOL available))completion {
    NSLog(@"[DynamicDomain] 测试域名可用性: %@", domain);

    // 构造测试URL
    NSString *testURL = [domain stringByAppendingPathComponent:@"Alicia/bigger"];

    // 创建请求管理器，设置更宽松的配置
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];

    // 设置响应序列化器
    AFJSONResponseSerializer *responseSerializer = [AFJSONResponseSerializer serializer];
    responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/json", @"text/javascript", @"text/plain", nil];
    manager.responseSerializer = responseSerializer;

    // 设置请求序列化器
    AFHTTPRequestSerializer *requestSerializer = [AFHTTPRequestSerializer serializer];
    requestSerializer.timeoutInterval = 12.0; // 12秒超时，给域名测试更多时间
    // 设置User-Agent
    [requestSerializer setValue:@"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15" forHTTPHeaderField:@"User-Agent"];
    manager.requestSerializer = requestSerializer;

    // 设置安全策略，允许HTTP请求
    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy defaultPolicy];
    securityPolicy.allowInvalidCertificates = YES;
    securityPolicy.validatesDomainName = NO;
    manager.securityPolicy = securityPolicy;

    // 构造测试参数
    NSDictionary *testParams = @{
        @"bigger": @"en",
        @"ofgwendoline": @(NO),
        @"outright": @(NO)
    };

    NSLog(@"[DynamicDomain] 测试URL: %@", testURL);

    [manager POST:testURL parameters:testParams headers:nil progress:nil
          success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSLog(@"[DynamicDomain] 域名 %@ 测试成功，响应: %@", domain, responseObject);
        if (completion) completion(YES);
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"[DynamicDomain] 域名 %@ 测试失败: %@", domain, error.localizedDescription);
        NSLog(@"[DynamicDomain] 错误详情: %@", error);
        if (completion) completion(NO);
    }];
}

- (void)testDomainConnectivity:(NSString *)domain completion:(void(^)(BOOL available))completion {
    NSLog(@"[DynamicDomain] 简单连通性测试: %@", domain);

    // 使用简单的GET请求测试连通性
    NSURL *url = [NSURL URLWithString:domain];
    if (!url) {
        NSLog(@"[DynamicDomain] 无效的URL: %@", domain);
        if (completion) completion(NO);
        return;
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.HTTPMethod = @"GET";
    request.timeoutInterval = 8.0; // 8秒超时
    [request setValue:@"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15" forHTTPHeaderField:@"User-Agent"];

    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"[DynamicDomain] 连通性测试失败 %@: %@", domain, error.localizedDescription);
            if (completion) completion(NO);
        } else {
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            BOOL isSuccess = (httpResponse.statusCode >= 200 && httpResponse.statusCode < 400);
            NSLog(@"[DynamicDomain] 连通性测试 %@ 状态码: %ld", domain, (long)httpResponse.statusCode);
            if (completion) completion(isSuccess);
        }
    }];

    [task resume];
}

- (void)resetToDefaultDomain {
    NSLog(@"[DynamicDomain] 重置到默认域名");
    self.currentDomain = self.defaultDomain;
    [self clearDomainCache];
}

- (nullable NSString *)getCachedAvailableDomain {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *cachedDomain = [defaults stringForKey:kCachedAvailableDomainKey];
    NSDate *cacheTimestamp = [defaults objectForKey:kDomainCacheTimestampKey];
    
    if (cachedDomain && cacheTimestamp) {
        NSTimeInterval timeSinceCache = [[NSDate date] timeIntervalSinceDate:cacheTimestamp];
        if (timeSinceCache < kDomainCacheValidDuration) {
            return cachedDomain;
        } else {
            NSLog(@"[DynamicDomain] 缓存域名已过期");
            [self clearDomainCache];
        }
    }
    
    return nil;
}

- (void)clearDomainCache {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults removeObjectForKey:kCachedAvailableDomainKey];
    [defaults removeObjectForKey:kDomainCacheTimestampKey];
    [defaults synchronize];
    NSLog(@"[DynamicDomain] 已清除域名缓存");
}

#pragma mark - Private Methods



- (void)testDomainsSequentially:(NSArray *)domainList
                   currentIndex:(NSUInteger)index
                     completion:(DynamicDomainCompletion)completion {
    if (index >= domainList.count) {
        NSLog(@"[DynamicDomain] 所有域名测试完毕，未找到可用域名");
        NSError *error = [NSError errorWithDomain:@"DynamicDomainManager"
                                             code:-2
                                         userInfo:@{NSLocalizedDescriptionKey: @"未找到可用域名"}];
        if (completion) completion(NO, nil, error);
        return;
    }

    NSString *domain = domainList[index];
    if (![domain isKindOfClass:[NSString class]] || domain.length == 0) {
        NSLog(@"[DynamicDomain] 跳过无效域名: %@", domain);
        [self testDomainsSequentially:domainList currentIndex:index + 1 completion:completion];
        return;
    }

    NSLog(@"[DynamicDomain] 测试域名 %lu/%lu: %@", (unsigned long)(index + 1), (unsigned long)domainList.count, domain);

    // 先进行简单的连通性测试
    [self testDomainConnectivity:domain completion:^(BOOL connectivityOK) {
        if (connectivityOK) {
            NSLog(@"[DynamicDomain] 域名 %@ 连通性测试通过，进行API测试", domain);
            // 连通性OK，再进行完整的API测试
            [self testDomainAvailability:domain completion:^(BOOL available) {
                if (available) {
                    NSLog(@"[DynamicDomain] 找到可用域名: %@", domain);
                    [self cacheAvailableDomain:domain];
                    self.currentDomain = domain;
                    if (completion) completion(YES, domain, nil);
                } else {
                    NSLog(@"[DynamicDomain] 域名 %@ API测试失败，继续下一个", domain);
                    // 继续测试下一个域名
                    [self testDomainsSequentially:domainList currentIndex:index + 1 completion:completion];
                }
            }];
        } else {
            NSLog(@"[DynamicDomain] 域名 %@ 连通性测试失败，跳过", domain);
            // 连通性测试失败，直接跳过
            [self testDomainsSequentially:domainList currentIndex:index + 1 completion:completion];
        }
    }];
}

- (void)cacheAvailableDomain:(NSString *)domain {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setObject:domain forKey:kCachedAvailableDomainKey];
    [defaults setObject:[NSDate date] forKey:kDomainCacheTimestampKey];
    [defaults synchronize];
    NSLog(@"[DynamicDomain] 已缓存可用域名: %@", domain);
}

- (void)cacheDynamicDomainJSON:(id)jsonObject {
    if (jsonObject) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:0 error:nil];
        if (jsonData) {
            [defaults setObject:jsonData forKey:kDynamicDomainJSONCacheKey];
            [defaults synchronize];
            NSLog(@"[DynamicDomain] 已缓存动态域名JSON");
        }
    }
}

- (NSArray *)getDomainListFromCache {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSData *jsonData = [defaults objectForKey:kDynamicDomainJSONCacheKey];

    if (jsonData) {
        NSError *error;
        id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
        if (!error && [jsonObject isKindOfClass:[NSArray class]]) {
            NSLog(@"[DynamicDomain] 从缓存中读取JSON成功");
            return [self extractDomainsFromJSON:jsonObject];
        } else {
            NSLog(@"[DynamicDomain] 缓存JSON解析失败: %@", error.localizedDescription);
        }
    } else {
        NSLog(@"[DynamicDomain] 无缓存的JSON数据");
    }

    return @[];
}

- (NSArray *)extractDomainsFromJSON:(NSArray *)jsonArray {
    NSMutableArray *domainList = [NSMutableArray array];

    // 解析域名配置，提取实际的域名字符串
    for (id item in jsonArray) {
        if ([item isKindOfClass:[NSDictionary class]]) {
            NSDictionary *domainConfig = (NSDictionary *)item;
            NSString *domain = domainConfig[@"nano"];
            if ([domain isKindOfClass:[NSString class]] && domain.length > 0) {
                // 确保域名以正确的格式结尾
                if ([domain hasSuffix:@"/"]) {
                    domain = [domain substringToIndex:domain.length - 1];
                }
                [domainList addObject:domain];
            }
        } else if ([item isKindOfClass:[NSString class]]) {
            // 兼容简单字符串格式
            NSString *domain = (NSString *)item;
            if (domain.length > 0) {
                [domainList addObject:domain];
            }
        }
    }

    NSLog(@"[DynamicDomain] 从JSON中提取出 %lu 个有效域名: %@", (unsigned long)domainList.count, domainList);
    return [domainList copy];
}

- (void)tryFallbackDomains:(DynamicDomainCompletion)completion {
    NSLog(@"[DynamicDomain] 使用备用域名列表");

    // 备用域名列表，基于已知的动态域名JSON内容
    NSArray *fallbackDomains = @[
        @"http://47.84.52.237/youtoday",
        @"https://nano.paperplane-lending.com/youtoday" // 默认域名作为最后备选
    ];

    NSLog(@"[DynamicDomain] 开始测试 %lu 个备用域名", (unsigned long)fallbackDomains.count);
    [self testDomainsSequentially:fallbackDomains currentIndex:0 completion:completion];
}

#pragma mark - Test Methods

- (void)testDynamicDomainFunction {
    NSLog(@"[DynamicDomain] 开始测试动态域名功能");

    // 1. 测试默认域名
    NSLog(@"[DynamicDomain] 默认域名: %@", self.defaultDomain);
    NSLog(@"[DynamicDomain] 动态域名配置URL: %@", self.dynamicDomainConfigURL);
    NSLog(@"[DynamicDomain] 当前使用域名: %@", self.currentDomain);

    // 2. 测试缓存功能
    NSArray *cachedDomains = [self getDomainListFromCache];
    NSLog(@"[DynamicDomain] 缓存的域名列表: %@", cachedDomains.count > 0 ? cachedDomains : @"无缓存");

    // 3. 测试JSON下载
    [self downloadAndCacheDynamicDomainJSON:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            NSLog(@"[DynamicDomain] JSON下载测试成功");
            // 4. 测试从缓存切换域名
            [self switchToAvailableDomainFromCache:^(BOOL success, NSString * _Nullable availableDomain, NSError * _Nullable error) {
                if (success) {
                    NSLog(@"[DynamicDomain] 域名切换测试成功 - 可用域名: %@", availableDomain);
                } else {
                    NSLog(@"[DynamicDomain] 域名切换测试失败 - 错误: %@", error.localizedDescription);
                }
            }];
        } else {
            NSLog(@"[DynamicDomain] JSON下载测试失败 - 错误: %@", error.localizedDescription);
        }
    }];
}

- (void)testDynamicDomainJSONFetch {
    NSLog(@"[DynamicDomain] 单独测试JSON获取");

    // 使用最简单的方式测试JSON获取
    NSURL *url = [NSURL URLWithString:self.dynamicDomainConfigURL];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.timeoutInterval = 20.0; // 20秒超时
    [request setValue:@"Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15" forHTTPHeaderField:@"User-Agent"];

    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"[DynamicDomain] JSON获取失败: %@", error.localizedDescription);
        } else {
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            NSLog(@"[DynamicDomain] JSON获取成功，状态码: %ld", (long)httpResponse.statusCode);

            if (data) {
                NSString *jsonString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                NSLog(@"[DynamicDomain] JSON内容: %@", jsonString);

                NSError *parseError;
                id jsonObject = [NSJSONSerialization JSONObjectWithData:data options:0 error:&parseError];
                if (parseError) {
                    NSLog(@"[DynamicDomain] JSON解析失败: %@", parseError.localizedDescription);
                } else {
                    NSLog(@"[DynamicDomain] JSON解析成功: %@", jsonObject);
                }
            }
        }
    }];

    [task resume];
}

@end
