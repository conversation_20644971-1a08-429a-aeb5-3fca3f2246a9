#import "LocationManager.h"
#import "NetworkManager.h"
#import <CoreLocation/CoreLocation.h>

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 15.0; // 15秒超时，快速获取位置
static const CLLocationAccuracy kBestAccuracy = 0.1; // 最佳精度0.1米（超高精度）
static const CLLocationAccuracy kGoodAccuracy = 0.3; // 良好精度0.3米
static const CLLocationAccuracy kAcceptableAccuracy = 1.0; // 可接受精度1米（降低门槛）
static const NSTimeInterval kLocationMaxAge = 0.5; // 位置数据最大有效期0.5秒（确保极高新鲜度）
static const NSTimeInterval kMinWaitTime = 3.0; // 最少等待3秒，快速响应位置变化

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@property (nonatomic, strong) CLLocation *bestLocation; // 当前最佳位置
@property (nonatomic, assign) BOOL hasReceivedFirstLocation; // 是否已收到第一个位置
@property (nonatomic, assign) NSInteger locationUpdateCount; // 位置更新次数
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@property (nonatomic, assign) NSTimeInterval lastReportTime; // 上次地址埋点上报时间
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    if (!completion) {
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置最高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 最高精度
    request.locationManager.distanceFilter = kCLDistanceFilterNone; // 不过滤任何距离变化，捕获微小移动

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"tracking" completion:nil];
        }
    }

    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

/// 强制刷新定位缓存 - 用于需要最新定位的场景
- (void)forceRefreshLocationCache:(LocationCompletionBlock)completion {
    [self getCurrentLocationForTracking:completion];
}

- (void)getCurrentLocationForRiskEvent:(LocationCompletionBlock)completion {
    // 风控埋点专用：强制获取最新高精度位置，不使用任何缓存
    NSLog(@"[埋点测试8-2] 风控埋点开始获取最新位置");

    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置超高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 最高精度
    request.locationManager.distanceFilter = kCLDistanceFilterNone; // 捕获任何距离变化

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"risk_tracking" completion:nil];
        }
    }

    [self.activeRequests addObject:request];

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSError *error = [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location access denied"}];
        [self completeRequest:request withCoordinate:kCLLocationCoordinate2DInvalid error:error];
        return;
    } else {
        // 立即开始定位
        [request.locationManager startUpdatingLocation];

        // 设置超时定时器（风控埋点使用更短的超时时间）
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self handleRequestTimeout:request];
        });
    }
}

- (void)reportLocationInfo {
    // 地址埋点防重复：如果距离上次上报不足5秒，则跳过
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - self.lastReportTime < 5.0) {
        NSLog(@"[埋点测试8-2] 地址埋点防重复：距离上次上报不足5秒，跳过本次上报");
        return;
    }

    [self getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        // 创建一个可修改的坐标副本
        CLLocationCoordinate2D finalCoordinate = coordinate;
        if (error) {
            // 即使定位失败，也尝试使用缓存坐标上报
            finalCoordinate = self.cachedCoordinate;
        }

        [self processLocationForReporting:finalCoordinate];
    }];
}

/// 处理坐标进行地址逆编码和上报
- (void)processLocationForReporting:(CLLocationCoordinate2D)coordinate {
    if (!CLLocationCoordinate2DIsValid(coordinate)) {
        NSLog(@"[埋点测试8-2] 坐标无效，跳过地址上报");
        return;
    }

    // 使用反向地理编码获取详细地址信息
    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
    CLLocation *location = [[CLLocation alloc] initWithLatitude:coordinate.latitude longitude:coordinate.longitude];

        [geocoder reverseGeocodeLocation:location completionHandler:^(NSArray<CLPlacemark *> * _Nullable placemarks, NSError * _Nullable geocodeError) {
            __block NSString *province = @"";     // 省
            __block NSString *city = @"";         // 市
            __block NSString *district = @"";     // 区
            __block NSString *street = @"";       // 街道
            __block NSString *country = @"";      // 国家
            __block NSString *countryCode = @"";  // 国家代码

            if (geocodeError) {
                NSLog(@"[埋点测试8-2] 系统地理编码失败: %@，尝试使用备用方案", geocodeError.localizedDescription);
                // 系统地理编码失败，尝试使用备用地理编码服务
                [self performBackupGeocoding:coordinate completion:^(NSDictionary * _Nullable addressInfo) {
                    if (addressInfo) {
                        province = addressInfo[@"province"] ?: @"";
                        city = addressInfo[@"city"] ?: @"";
                        district = addressInfo[@"district"] ?: @"";
                        street = addressInfo[@"street"] ?: @"";
                        country = addressInfo[@"country"] ?: @"";
                        countryCode = addressInfo[@"countryCode"] ?: @"";
                        NSLog(@"[埋点测试8-2] 备用地理编码成功: %@, %@", country, countryCode);
                    } else {
                        NSLog(@"[埋点测试8-2] 备用地理编码也失败，使用基于坐标的国家判断");
                        // 备用方案也失败，根据经纬度范围判断可能的国家
                        NSDictionary *estimatedCountry = [self estimateCountryFromCoordinate:coordinate];
                        country = estimatedCountry[@"country"] ?: @"Unknown";
                        countryCode = estimatedCountry[@"countryCode"] ?: @"XX";
                    }

                    // 执行地址上报
                    [self reportAddressWithCoordinate:coordinate province:province city:city district:district street:street country:country countryCode:countryCode];
                }];
                return; // 异步处理，直接返回
            } else if (placemarks.count > 0) {
                CLPlacemark *placemark = placemarks.firstObject;

                // 提取地址信息 - 使用更全面的字段映射
                province = [self extractProvinceFromPlacemark:placemark];
                city = [self extractCityFromPlacemark:placemark];
                district = [self extractDistrictFromPlacemark:placemark];
                street = [self extractStreetFromPlacemark:placemark];
                country = placemark.country ?: @"";                      // 国家
                countryCode = placemark.ISOcountryCode ?: @"";           // 国家代码

                NSLog(@"[埋点测试8-2] 系统地理编码结果: 国家=%@(%@), 省=%@, 市=%@, 区=%@, 街道=%@",
                      country, countryCode, province, city, district, street);

                // 检查详细地址信息的完整性
                BOOL hasDetailedAddress = (province.length > 0 || city.length > 0 || district.length > 0 || street.length > 0);

                if (!hasDetailedAddress && country.length > 0) {
                    NSLog(@"[埋点测试8-2] 系统地理编码只返回国家信息，尝试备用服务获取详细地址");
                    // 只有国家信息，尝试备用服务获取更详细的地址
                    [self performBackupGeocoding:coordinate completion:^(NSDictionary * _Nullable addressInfo) {
                        if (addressInfo) {
                            // 合并系统和备用服务的结果，优先使用更详细的信息
                            province = [self selectBetterValue:province backup:addressInfo[@"province"]];
                            city = [self selectBetterValue:city backup:addressInfo[@"city"]];
                            district = [self selectBetterValue:district backup:addressInfo[@"district"]];
                            street = [self selectBetterValue:street backup:addressInfo[@"street"]];

                            // 国家信息优先使用系统返回的，因为更准确
                            if (country.length == 0) {
                                country = addressInfo[@"country"] ?: @"";
                            }
                            if (countryCode.length == 0) {
                                countryCode = addressInfo[@"countryCode"] ?: @"";
                            }

                            NSLog(@"[埋点测试8-2] 合并备用服务结果: 省=%@, 市=%@, 区=%@, 街道=%@",
                                  province, city, district, street);
                        }

                        // 执行地址上报
                        [self reportAddressWithCoordinate:coordinate province:province city:city district:district street:street country:country countryCode:countryCode];
                    }];
                    return; // 异步处理
                }

                // 验证地址信息的合理性
                if ([self validateAddressInfo:country countryCode:countryCode coordinate:coordinate]) {
                    NSLog(@"[埋点测试8-2] 地址信息验证通过");
                } else {
                    NSLog(@"[埋点测试8-2] 地址信息验证失败，可能存在位置偏差");
                }
            } else {
                NSLog(@"[埋点测试8-2] 系统地理编码返回空结果，使用备用方案");
                // 没有返回结果，使用备用方案
                [self performBackupGeocoding:coordinate completion:^(NSDictionary * _Nullable addressInfo) {
                    if (addressInfo) {
                        province = addressInfo[@"province"] ?: @"";
                        city = addressInfo[@"city"] ?: @"";
                        district = addressInfo[@"district"] ?: @"";
                        street = addressInfo[@"street"] ?: @"";
                        country = addressInfo[@"country"] ?: @"";
                        countryCode = addressInfo[@"countryCode"] ?: @"";
                    } else {
                        // 最后的备用方案
                        NSDictionary *estimatedCountry = [self estimateCountryFromCoordinate:coordinate];
                        country = estimatedCountry[@"country"] ?: @"Unknown";
                        countryCode = estimatedCountry[@"countryCode"] ?: @"XX";
                    }

                    // 执行地址上报
                    [self reportAddressWithCoordinate:coordinate province:province city:city district:district street:street country:country countryCode:countryCode];
                }];
                return; // 异步处理，直接返回
            }

            // 同步处理的情况，直接执行地址上报
            [self reportAddressWithCoordinate:coordinate province:province city:city district:district street:street country:country countryCode:countryCode];
        }];
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            return coordinate;
        }
    }
    // 返回无效坐标，表示没有缓存数据
    return kCLLocationCoordinate2DInvalid;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        return;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    CLLocationCoordinate2D finalCoordinate;
    NSError *error = nil;

    // 优先使用本次定位过程中获得的最佳位置
    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        } else {
            finalCoordinate = kCLLocationCoordinate2DInvalid;
            error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, no valid coordinate available"}];
        }
    }

    // 回调结果
    if (request.completion) {
        request.completion(finalCoordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        return;
    }

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 检查位置数据的新鲜度
    NSTimeInterval locationAge = -[location.timestamp timeIntervalSinceNow];
    if (locationAge > kLocationMaxAge) {
        return;
    }

    // 检查位置精度有效性
    if (location.horizontalAccuracy < 0) {
        return;
    }

    request.locationUpdateCount++;
    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;

    // 智能精度判断逻辑
    BOOL shouldAcceptLocation = [self shouldAcceptLocation:location forRequest:request elapsedTime:elapsedTime];

    if (!shouldAcceptLocation) {
        // 更新最佳位置记录
        if (!request.bestLocation || location.horizontalAccuracy < request.bestLocation.horizontalAccuracy) {
            request.bestLocation = location;
        }
        return;
    }

    // 选择最终位置
    CLLocation *finalLocation = request.bestLocation && request.bestLocation.horizontalAccuracy < location.horizontalAccuracy ? request.bestLocation : location;

    // 保存到缓存
    [self saveCachedCoordinate:finalLocation.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(finalLocation.coordinate, nil);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 使用缓存坐标回调
    if (request.completion) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        request.completion(coordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            if (request.completion) {
                CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
                request.completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            break;
        default:
            break;
    }
}

#pragma mark - Helper Methods

/// 智能判断是否应该接受当前位置 - 针对高精度实时定位优化
- (BOOL)shouldAcceptLocation:(CLLocation *)location forRequest:(LocationRequest *)request elapsedTime:(NSTimeInterval)elapsedTime {
    // 第一次收到位置
    if (!request.hasReceivedFirstLocation) {
        request.hasReceivedFirstLocation = YES;
    }

    // 立即接受的条件：亚米级精度（0.5米以内）
    if (location.horizontalAccuracy <= kBestAccuracy) {
        return YES;
    }

    // 良好精度 + 等待时间足够 + 收到少量更新（降低门槛）
    if (location.horizontalAccuracy <= kGoodAccuracy && elapsedTime >= kMinWaitTime && request.locationUpdateCount >= 2) {
        return YES;
    }

    // 可接受精度 + 等待时间较短 + 收到更新（大幅降低门槛）
    if (location.horizontalAccuracy <= kAcceptableAccuracy && elapsedTime >= kMinWaitTime && request.locationUpdateCount >= 2) {
        return YES;
    }

    // 超过70%超时时间，使用最佳可用位置（更早接受位置）
    if (elapsedTime >= (kLocationTimeout * 0.7)) {
        return YES;
    }

    // 如果已经等待了5秒且有任何有效位置，就接受（新增快速接受逻辑）
    if (elapsedTime >= 5.0 && location.horizontalAccuracy <= 5.0) {
        return YES;
    }

    // 继续等待更好的精度
    return NO;
}

#pragma mark - Missing Methods

- (void)completeRequest:(LocationRequest *)request withCoordinate:(CLLocationCoordinate2D)coordinate error:(NSError *)error {
    // 完成单个请求
    if (![self.activeRequests containsObject:request]) {
        return; // 请求已经完成或不存在
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];

    // 执行回调
    if (request.completion) {
        request.completion(coordinate, error);
    }
}

- (void)handleRequestTimeout:(LocationRequest *)request {
    // 处理请求超时
    if (![self.activeRequests containsObject:request]) {
        return; // 请求已经完成
    }

    [request.locationManager stopUpdatingLocation];

    // 优先使用本次定位过程中获得的最佳位置
    CLLocationCoordinate2D finalCoordinate = kCLLocationCoordinate2DInvalid;
    NSError *error = nil;

    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
        }
        error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Timeout without valid location"}];
    }

    [self completeRequest:request withCoordinate:finalCoordinate error:error];
}

#pragma mark - Address Geocoding Helper Methods

/// 执行地址信息上报
- (void)reportAddressWithCoordinate:(CLLocationCoordinate2D)coordinate
                           province:(NSString *)province
                               city:(NSString *)city
                           district:(NSString *)district
                             street:(NSString *)street
                            country:(NSString *)country
                        countryCode:(NSString *)countryCode {

    // 构建上报参数
    NSDictionary *params = @{
        @"darrein": province ?: @"",                    // 省
        @"italways": countryCode ?: @"",                // 国家code
        @"unkindly": country ?: @"",                    // 国家
        @"askedme": street ?: @"",                      // 街道
        @"invitation": [NSString stringWithFormat:@"%.6f", coordinate.latitude],   // 纬度
        @"hersuch": [NSString stringWithFormat:@"%.6f", coordinate.longitude],     // 经度
        @"sharethe": city ?: @"",                       // 市
        @"adoration": district ?: @""                   // 区
    };

    // 更新上次上报时间
    self.lastReportTime = [[NSDate date] timeIntervalSince1970];

    NSLog(@"[埋点测试8-2] 埋点-地址，使用的经度%.6f，纬度%.6f，国家：%@(%@)",
          coordinate.longitude, coordinate.latitude, country, countryCode);

    [NetworkManager postFormWithAPI:@"Alicia/cardboardcontainers" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable networkError) {
        if (!networkError) {
            NSLog(@"[埋点测试8-2] 埋点-地址 上报成功");
        } else {
            NSLog(@"[埋点测试8-2] 埋点-地址 上报失败: %@", networkError.localizedDescription);
        }
    }];
}

/// 备用地理编码服务（使用第三方API或自建服务）
- (void)performBackupGeocoding:(CLLocationCoordinate2D)coordinate completion:(void(^)(NSDictionary * _Nullable addressInfo))completion {
    // 这里可以集成第三方地理编码服务，比如高德、百度、Google等
    // 为了演示，这里使用一个简单的HTTP请求到免费的地理编码服务

    NSString *urlString = [NSString stringWithFormat:@"https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=%.6f&longitude=%.6f&localityLanguage=en",
                          coordinate.latitude, coordinate.longitude];

    NSURL *url = [NSURL URLWithString:urlString];
    NSURLRequest *request = [NSURLRequest requestWithURL:url cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:10.0];

    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error || !data) {
                NSLog(@"[埋点测试8-2] 备用地理编码请求失败: %@", error.localizedDescription);
                completion(nil);
                return;
            }

            NSError *jsonError;
            NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
            if (jsonError || !json) {
                NSLog(@"[埋点测试8-2] 备用地理编码JSON解析失败: %@", jsonError.localizedDescription);
                completion(nil);
                return;
            }

            // 解析返回的地址信息 - 使用更全面的字段映射
            NSString *country = json[@"countryName"] ?: @"";
            NSString *countryCode = json[@"countryCode"] ?: @"";
            NSString *province = [self extractProvinceFromBackupAPI:json];
            NSString *city = [self extractCityFromBackupAPI:json];
            NSString *district = [self extractDistrictFromBackupAPI:json];
            NSString *street = [self extractStreetFromBackupAPI:json];

            NSDictionary *addressInfo = @{
                @"country": country,
                @"countryCode": countryCode,
                @"province": province,
                @"city": city,
                @"district": district,
                @"street": street
            };

            NSLog(@"[埋点测试8-2] 备用地理编码解析结果: 国家=%@(%@), 省=%@, 市=%@, 区=%@, 街道=%@",
                  country, countryCode, province, city, district, street);
            completion(addressInfo);
        });
    }];

    [task resume];
}

/// 根据经纬度估算国家（基于地理范围的粗略判断）
- (NSDictionary *)estimateCountryFromCoordinate:(CLLocationCoordinate2D)coordinate {
    double lat = coordinate.latitude;
    double lng = coordinate.longitude;

    // 基于经纬度范围的粗略国家判断
    // 中国大陆范围：纬度18°-54°，经度73°-135°
    if (lat >= 18.0 && lat <= 54.0 && lng >= 73.0 && lng <= 135.0) {
        return @{@"country": @"China", @"countryCode": @"CN"};
    }

    // 越南范围：纬度8°-24°，经度102°-110°
    if (lat >= 8.0 && lat <= 24.0 && lng >= 102.0 && lng <= 110.0) {
        return @{@"country": @"Vietnam", @"countryCode": @"VN"};
    }

    // 泰国范围：纬度5°-21°，经度97°-106°
    if (lat >= 5.0 && lat <= 21.0 && lng >= 97.0 && lng <= 106.0) {
        return @{@"country": @"Thailand", @"countryCode": @"TH"};
    }

    // 印度尼西亚范围：纬度-11°-6°，经度95°-141°
    if (lat >= -11.0 && lat <= 6.0 && lng >= 95.0 && lng <= 141.0) {
        return @{@"country": @"Indonesia", @"countryCode": @"ID"};
    }

    // 菲律宾范围：纬度4°-21°，经度116°-127°
    if (lat >= 4.0 && lat <= 21.0 && lng >= 116.0 && lng <= 127.0) {
        return @{@"country": @"Philippines", @"countryCode": @"PH"};
    }

    // 马来西亚范围：纬度1°-7°，经度99°-119°
    if (lat >= 1.0 && lat <= 7.0 && lng >= 99.0 && lng <= 119.0) {
        return @{@"country": @"Malaysia", @"countryCode": @"MY"};
    }

    // 新加坡范围：纬度1.1°-1.5°，经度103.6°-104.0°
    if (lat >= 1.1 && lat <= 1.5 && lng >= 103.6 && lng <= 104.0) {
        return @{@"country": @"Singapore", @"countryCode": @"SG"};
    }

    // 默认返回未知
    NSLog(@"[埋点测试8-2] 无法根据坐标(%.6f, %.6f)估算国家", lat, lng);
    return @{@"country": @"Unknown", @"countryCode": @"XX"};
}

/// 验证地址信息的合理性
- (BOOL)validateAddressInfo:(NSString *)country countryCode:(NSString *)countryCode coordinate:(CLLocationCoordinate2D)coordinate {
    if (!country || country.length == 0 || !countryCode || countryCode.length == 0) {
        return NO;
    }

    // 获取基于坐标的估算国家
    NSDictionary *estimatedCountry = [self estimateCountryFromCoordinate:coordinate];
    NSString *estimatedCountryCode = estimatedCountry[@"countryCode"];

    // 如果估算的国家代码与返回的国家代码不匹配，可能存在问题
    if (![countryCode isEqualToString:estimatedCountryCode]) {
        NSLog(@"[埋点测试8-2] 地址验证警告：返回国家代码(%@)与坐标估算(%@)不匹配", countryCode, estimatedCountryCode);
        return NO;
    }

    return YES;
}

#pragma mark - Address Extraction Helper Methods

/// 从CLPlacemark中提取省/州信息
- (NSString *)extractProvinceFromPlacemark:(CLPlacemark *)placemark {
    // 安全地尝试多个字段来获取省/州信息
    NSArray *provinceFields = @[
        placemark.administrativeArea ?: @"",           // 主要的行政区域（省/州）
        placemark.subAdministrativeArea ?: @"",       // 次级行政区域
        placemark.locality ?: @""                      // 如果没有省信息，城市可能包含省级信息
    ];

    for (NSString *field in provinceFields) {
        if (field.length > 0) {
            return field;
        }
    }
    return @"";
}

/// 从CLPlacemark中提取城市信息
- (NSString *)extractCityFromPlacemark:(CLPlacemark *)placemark {
    // 安全地尝试多个字段来获取城市信息
    NSArray *cityFields = @[
        placemark.locality ?: @"",                     // 主要的城市/地区
        placemark.subAdministrativeArea ?: @"",       // 次级行政区域（有时是城市）
        placemark.subLocality ?: @"",                  // 子地区
        placemark.administrativeArea ?: @""            // 如果locality为空，有时administrativeArea包含城市信息
    ];

    for (NSString *field in cityFields) {
        if (field.length > 0) {
            return field;
        }
    }
    return @"";
}

/// 从CLPlacemark中提取区/县信息
- (NSString *)extractDistrictFromPlacemark:(CLPlacemark *)placemark {
    // 安全地尝试多个字段来获取区/县信息
    NSArray *districtFields = @[
        placemark.subLocality ?: @"",                  // 子地区（通常是区/县）
        placemark.thoroughfare ?: @"",                 // 有时包含区域信息
        placemark.subThoroughfare ?: @""              // 子街道信息
    ];

    for (NSString *field in districtFields) {
        if (field.length > 0) {
            return field;
        }
    }
    return @"";
}

/// 从CLPlacemark中提取街道信息
- (NSString *)extractStreetFromPlacemark:(CLPlacemark *)placemark {
    // 安全地尝试多个字段来获取街道信息
    NSArray *streetFields = @[
        placemark.thoroughfare ?: @"",                 // 主要街道
        placemark.subThoroughfare ?: @"",             // 子街道/门牌号
        placemark.name ?: @"",                        // 地点名称
        placemark.areasOfInterest.firstObject ?: @""  // 兴趣点
    ];

    for (NSString *field in streetFields) {
        if (field.length > 0) {
            return field;
        }
    }
    return @"";
}

/// 选择更好的地址值（优先选择非空且更详细的）
- (NSString *)selectBetterValue:(NSString *)systemValue backup:(NSString *)backupValue {
    // 如果系统值为空或很短，优先使用备用值
    if (!systemValue || systemValue.length == 0) {
        return backupValue ?: @"";
    }

    // 如果备用值为空，使用系统值
    if (!backupValue || backupValue.length == 0) {
        return systemValue;
    }

    // 都不为空时，选择更长的（通常更详细）
    if (backupValue.length > systemValue.length) {
        return backupValue;
    }

    return systemValue;
}

#pragma mark - Backup API Address Extraction Methods

/// 从备用API响应中提取省/州信息
- (NSString *)extractProvinceFromBackupAPI:(NSDictionary *)json {
    // 安全地尝试多个字段来获取省/州信息
    NSArray *possibleValues = @[
        [self safeStringFromJSON:json key:@"principalSubdivision"],
        [self safeStringFromJSON:json key:@"principalSubdivisionCode"],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @0, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @1, @"name"]],
        [self safeStringFromJSON:json key:@"locality"]
    ];

    for (NSString *value in possibleValues) {
        if (value.length > 0) {
            return value;
        }
    }
    return @"";
}

/// 从备用API响应中提取城市信息
- (NSString *)extractCityFromBackupAPI:(NSDictionary *)json {
    // 安全地尝试多个字段来获取城市信息
    NSArray *possibleValues = @[
        [self safeStringFromJSON:json key:@"city"],
        [self safeStringFromJSON:json key:@"locality"],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @2, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @1, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"plusCode", @"locality"]]
    ];

    for (NSString *value in possibleValues) {
        if (value.length > 0) {
            return value;
        }
    }
    return @"";
}

/// 从备用API响应中提取区/县信息
- (NSString *)extractDistrictFromBackupAPI:(NSDictionary *)json {
    // 安全地尝试多个字段来获取区/县信息
    NSArray *possibleValues = @[
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @3, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @2, @"name"]],
        [self safeStringFromJSON:json key:@"postcode"],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"informative", @0, @"name"]]
    ];

    for (NSString *value in possibleValues) {
        if (value.length > 0) {
            return value;
        }
    }
    return @"";
}

/// 从备用API响应中提取街道信息
- (NSString *)extractStreetFromBackupAPI:(NSDictionary *)json {
    // 安全地尝试多个字段来获取街道信息
    NSArray *possibleValues = @[
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"informative", @1, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"informative", @0, @"name"]],
        [self safeStringFromNestedJSON:json path:@[@"plusCode", @"compound"]],
        [self safeStringFromNestedJSON:json path:@[@"localityInfo", @"administrative", @4, @"name"]]
    ];

    for (NSString *value in possibleValues) {
        if (value.length > 0) {
            return value;
        }
    }
    return @"";
}

#pragma mark - JSON Safety Helper Methods

/// 安全地从JSON中获取字符串值
- (NSString *)safeStringFromJSON:(NSDictionary *)json key:(NSString *)key {
    id value = json[key];
    if ([value isKindOfClass:[NSString class]]) {
        return (NSString *)value;
    }
    return @"";
}

/// 安全地从嵌套JSON中获取字符串值
- (NSString *)safeStringFromNestedJSON:(NSDictionary *)json path:(NSArray *)path {
    id current = json;

    for (id key in path) {
        if ([current isKindOfClass:[NSDictionary class]]) {
            current = [(NSDictionary *)current objectForKey:key];
        } else if ([current isKindOfClass:[NSArray class]]) {
            if ([key isKindOfClass:[NSNumber class]]) {
                NSInteger index = [(NSNumber *)key integerValue];
                NSArray *array = (NSArray *)current;
                if (index >= 0 && index < array.count) {
                    current = array[index];
                } else {
                    return @"";
                }
            } else {
                return @"";
            }
        } else {
            return @"";
        }

        if (!current) {
            return @"";
        }
    }

    if ([current isKindOfClass:[NSString class]]) {
        return (NSString *)current;
    }

    return @"";
}

@end
